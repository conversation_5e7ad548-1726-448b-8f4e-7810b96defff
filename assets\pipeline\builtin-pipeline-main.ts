import {
    assert,
    gfx, Layers, Material,
    renderer,
    rendering
} from 'cc';

import { DEBUG } from 'cc/env';

import { makePipelineSettings, PipelineSettings } from './builtin-pipeline-types';
import {
    CameraConfigs,
    PipelineConfigs,
    PipelineContext
} from './pipeline-configs';
import {
    forwardNeedClearColor, setupPipelineConfigs, sortPipelinePassBuildersByConfigOrder,
    sortPipelinePassBuildersByRenderOrder
} from './pipeline-utils';

// Import all pass builders
import { BuiltinBloomPassBuilder } from './bloom-pass';
import { BuiltinForwardPassBuilder } from './forward-pass';
import { BuiltinFsrPassBuilder } from './fsr-pass';
import { BuiltinFXAAPassBuilder } from './fxaa-pass';
import { BuiltinToneMappingPassBuilder } from './tone-mapping-pass';
import { BuiltinUiPassBuilder } from './ui-pass';

const { ClearFlagBit, Color, Format, LoadOp, StoreOp, Viewport } = gfx;
const { CameraUsage } = renderer.scene;

if (rendering) {

    const { QueueHint, SceneFlags } = rendering;

    // Create default settings
    const defaultSettings = makePipelineSettings();

    /**
     * 内置渲染管线构建器
     */
    class BuiltinPipelineBuilder implements rendering.PipelineBuilder {
        private readonly _forwardPass = new BuiltinForwardPassBuilder();
        private readonly _bloomPass = new BuiltinBloomPassBuilder();
        private readonly _toneMappingPass = new BuiltinToneMappingPassBuilder();
        private readonly _fxaaPass = new BuiltinFXAAPassBuilder();
        private readonly _fsrPass = new BuiltinFsrPassBuilder();
        private readonly _uiPass = new BuiltinUiPassBuilder();
        // Internal cached resources
        private readonly _clearColor = new Color(0, 0, 0, 1);
        private readonly _viewport = new Viewport();
        private readonly _configs = new PipelineConfigs();
        private readonly _cameraConfigs = new CameraConfigs();
        // Materials
        private readonly _copyAndTonemapMaterial = new Material();

        // Internal States
        private _initialized = false; // TODO(zhouzhenglong): Make default effect asset loading earlier and remove this flag
        private readonly _passBuilders: rendering.PipelinePassBuilder[] = [];

        private _setupPipelinePreview(
            camera: renderer.scene.Camera,
            cameraConfigs: CameraConfigs) {
            const isEditorView: boolean
                = camera.cameraUsage === CameraUsage.SCENE_VIEW
                || camera.cameraUsage === CameraUsage.PREVIEW;

            if (isEditorView) {
                const editorSettings = rendering.getEditorPipelineSettings() as PipelineSettings | null;
                if (editorSettings) {
                    cameraConfigs.settings = editorSettings;
                } else {
                    cameraConfigs.settings = defaultSettings;
                }
            } else {
                if (camera.pipelineSettings) {
                    cameraConfigs.settings = camera.pipelineSettings as PipelineSettings;
                } else {
                    cameraConfigs.settings = defaultSettings;
                }
            }
        }

        private _preparePipelinePasses(cameraConfigs: CameraConfigs): void {
            const passBuilders = this._passBuilders;
            passBuilders.length = 0;

            const settings = cameraConfigs.settings as PipelineSettings;

            passBuilders.push(this._forwardPass);

            if (settings.bloom.enabled) {
                passBuilders.push(this._bloomPass);
            }

            passBuilders.push(this._toneMappingPass);

            if (settings.fxaa.enabled) {
                passBuilders.push(this._fxaaPass);
            }

            if (settings.fsr.enabled) {
                passBuilders.push(this._fsrPass);
            }
            passBuilders.push(this._uiPass);
        }

        private _setupBuiltinCameraConfigs(
            camera: renderer.scene.Camera,
            pipelineConfigs: PipelineConfigs,
            cameraConfigs: CameraConfigs
        ) {
            const window = camera.window;
            const isMainGameWindow: boolean = camera.cameraUsage === CameraUsage.GAME && !!window.swapchain;

            // Window
            cameraConfigs.isMainGameWindow = isMainGameWindow;
            cameraConfigs.renderWindowId = window.renderWindowId;

            // Camera
            cameraConfigs.colorName = window.colorName;
            cameraConfigs.depthStencilName = window.depthStencilName;

            // Pipeline
            cameraConfigs.enableFullPipeline = (camera.visibility & (Layers.Enum.DEFAULT)) !== 0;
            cameraConfigs.enableProfiler = DEBUG && isMainGameWindow;
            cameraConfigs.remainingPasses = 0;

            // Shading scale
            cameraConfigs.shadingScale = cameraConfigs.settings.shadingScale;
            cameraConfigs.enableShadingScale = cameraConfigs.settings.enableShadingScale
                && cameraConfigs.shadingScale !== 1.0;

            cameraConfigs.nativeWidth = Math.max(Math.floor(window.width), 1);
            cameraConfigs.nativeHeight = Math.max(Math.floor(window.height), 1);

            cameraConfigs.width = cameraConfigs.enableShadingScale
                ? Math.max(Math.floor(cameraConfigs.nativeWidth * cameraConfigs.shadingScale), 1)
                : cameraConfigs.nativeWidth;
            cameraConfigs.height = cameraConfigs.enableShadingScale
                ? Math.max(Math.floor(cameraConfigs.nativeHeight * cameraConfigs.shadingScale), 1)
                : cameraConfigs.nativeHeight;

            // Radiance
            cameraConfigs.enableHDR = cameraConfigs.enableFullPipeline
                && pipelineConfigs.useFloatOutput;
            cameraConfigs.radianceFormat = cameraConfigs.enableHDR
                ? gfx.Format.RGBA16F : gfx.Format.RGBA8;

            // Tone Mapping
            cameraConfigs.copyAndTonemapMaterial = this._copyAndTonemapMaterial;

            // Depth
            cameraConfigs.enableStoreSceneDepth = false;
        }

        private _setupCameraConfigs(
            camera: renderer.scene.Camera,
            pipelineConfigs: PipelineConfigs,
            cameraConfigs: CameraConfigs
        ): void {
            this._setupPipelinePreview(camera, cameraConfigs);

            this._preparePipelinePasses(cameraConfigs);

            sortPipelinePassBuildersByConfigOrder(this._passBuilders);

            this._setupBuiltinCameraConfigs(camera, pipelineConfigs, cameraConfigs);

            for (const builder of this._passBuilders) {
                if (builder.configCamera) {
                    builder.configCamera(camera, pipelineConfigs, cameraConfigs);
                }
            }
        }

        // ----------------------------------------------------------------
        // Interface
        // ----------------------------------------------------------------
        windowResize(
            ppl: rendering.BasicPipeline,
            window: renderer.RenderWindow,
            camera: renderer.scene.Camera,
            nativeWidth: number,
            nativeHeight: number,
        ): void {
            setupPipelineConfigs(ppl, this._configs);

            this._setupCameraConfigs(camera, this._configs, this._cameraConfigs);

            // Render Window (UI)
            const id = window.renderWindowId;

            ppl.addRenderWindow(this._cameraConfigs.colorName,
                Format.RGBA8, nativeWidth, nativeHeight, window,
                this._cameraConfigs.depthStencilName);

            const width = this._cameraConfigs.width;
            const height = this._cameraConfigs.height;

            if (this._cameraConfigs.enableShadingScale) {
                ppl.addDepthStencil(`ScaledSceneDepth_${id}`, Format.DEPTH_STENCIL, width, height);
                ppl.addRenderTarget(`ScaledRadiance0_${id}`, this._cameraConfigs.radianceFormat, width, height);
                ppl.addRenderTarget(`ScaledRadiance1_${id}`, this._cameraConfigs.radianceFormat, width, height);
                ppl.addRenderTarget(`ScaledLdrColor0_${id}`, Format.RGBA8, width, height);
                ppl.addRenderTarget(`ScaledLdrColor1_${id}`, Format.RGBA8, width, height);
            } else {
                ppl.addDepthStencil(`SceneDepth_${id}`, Format.DEPTH_STENCIL, width, height);
                ppl.addRenderTarget(`Radiance0_${id}`, this._cameraConfigs.radianceFormat, width, height);
                ppl.addRenderTarget(`Radiance1_${id}`, this._cameraConfigs.radianceFormat, width, height);
                ppl.addRenderTarget(`LdrColor0_${id}`, Format.RGBA8, width, height);
                ppl.addRenderTarget(`LdrColor1_${id}`, Format.RGBA8, width, height);
            }
            ppl.addRenderTarget(`UiColor0_${id}`, Format.RGBA8, nativeWidth, nativeHeight);
            ppl.addRenderTarget(`UiColor1_${id}`, Format.RGBA8, nativeWidth, nativeHeight);

            for (const builder of this._passBuilders) {
                if (builder.windowResize) {
                    builder.windowResize(ppl, this._configs, this._cameraConfigs, window, camera, nativeWidth, nativeHeight);
                }
            }
        }
        setup(cameras: renderer.scene.Camera[], ppl: rendering.BasicPipeline): void {
            // TODO(zhouzhenglong): Make default effect asset loading earlier and remove _initMaterials
            if (this._initMaterials(ppl)) {
                return;
            }
            // Render cameras
            // log(`==================== One Frame ====================`);
            for (const camera of cameras) {
                // Skip invalid camera
                if (!camera.scene || !camera.window) {
                    continue;
                }
                // Setup camera configs
                this._setupCameraConfigs(camera, this._configs, this._cameraConfigs);
                // log(`Setup camera: ${camera.node!.name}, window: ${camera.window.renderWindowId}, isFull: ${this._cameraConfigs.enableFullPipeline}, `
                //     + `size: ${camera.window.width}x${camera.window.height}`);

                // Build pipeline
                if (this._cameraConfigs.enableFullPipeline) {
                    this._buildForwardPipeline(ppl, camera, camera.scene, this._passBuilders);
                } else {
                    this._buildSimplePipeline(ppl, camera);
                }
            }
        }
        // ----------------------------------------------------------------
        // Pipelines
        // ----------------------------------------------------------------
        private _buildSimplePipeline(
            ppl: rendering.BasicPipeline,
            camera: renderer.scene.Camera,
        ): void {
            const width = Math.max(Math.floor(camera.window.width), 1);
            const height = Math.max(Math.floor(camera.window.height), 1);
            const colorName = this._cameraConfigs.colorName;
            const depthStencilName = this._cameraConfigs.depthStencilName;

            const viewport = camera.viewport;  // Reduce C++/TS interop
            this._viewport.left = Math.round(viewport.x * width);
            this._viewport.top = Math.round(viewport.y * height);
            // Here we must use camera.viewport.width instead of camera.viewport.z, which
            // is undefined on native platform. The same as camera.viewport.height.
            this._viewport.width = Math.max(Math.round(viewport.width * width), 1);
            this._viewport.height = Math.max(Math.round(viewport.height * height), 1);

            const clearColor = camera.clearColor;  // Reduce C++/TS interop
            this._clearColor.x = clearColor.x;
            this._clearColor.y = clearColor.y;
            this._clearColor.z = clearColor.z;
            this._clearColor.w = clearColor.w;

            const pass = ppl.addRenderPass(width, height, 'default');

            // bind output render target
            if (forwardNeedClearColor(camera)) {
                pass.addRenderTarget(colorName, LoadOp.CLEAR, StoreOp.STORE, this._clearColor);
            } else {
                pass.addRenderTarget(colorName, LoadOp.LOAD, StoreOp.STORE);
            }

            // bind depth stencil buffer
            if (camera.clearFlag & ClearFlagBit.DEPTH_STENCIL) {
                pass.addDepthStencil(
                    depthStencilName,
                    LoadOp.CLEAR,
                    StoreOp.DISCARD,
                    camera.clearDepth,
                    camera.clearStencil,
                    camera.clearFlag & ClearFlagBit.DEPTH_STENCIL,
                );
            } else {
                pass.addDepthStencil(depthStencilName, LoadOp.LOAD, StoreOp.DISCARD);
            }

            pass.setViewport(this._viewport);

            // The opaque queue is used for Reflection probe preview
            pass.addQueue(QueueHint.OPAQUE)
                .addScene(camera, SceneFlags.OPAQUE);

            // The blend queue is used for UI and Gizmos
            let flags = SceneFlags.BLEND | SceneFlags.UI;
            if (this._cameraConfigs.enableProfiler) {
                flags |= SceneFlags.PROFILER;
                pass.showStatistics = true;
            }
            pass.addQueue(QueueHint.BLEND)
                .addScene(camera, flags);
        }

        private _buildForwardPipeline(
            ppl: rendering.BasicPipeline,
            camera: renderer.scene.Camera,
            scene: renderer.RenderScene,
            passBuilders: rendering.PipelinePassBuilder[],
        ): void {
            sortPipelinePassBuildersByRenderOrder(passBuilders);

            const context: PipelineContext = {
                colorName: '',
                depthStencilName: '',
            };

            let lastPass: rendering.BasicRenderPassBuilder | undefined = undefined;

            for (const builder of passBuilders) {
                if (builder.setup) {
                    lastPass = builder.setup(ppl, this._configs, this._cameraConfigs,
                        camera, context, lastPass);
                }
            }

            assert(this._cameraConfigs.remainingPasses === 0);
        }

        private _initMaterials(ppl: rendering.BasicPipeline): number {
            if (this._initialized) {
                return 0;
            }

            setupPipelineConfigs(ppl, this._configs);

            // When add new effect asset, please add its uuid to the dependentAssets in cc.config.json.
            this._copyAndTonemapMaterial._uuid = `builtin-pipeline-tone-mapping-material`;
            this._copyAndTonemapMaterial.initialize({ effectName: 'pipeline/post-process/tone-mapping' });

            if (this._copyAndTonemapMaterial.effectAsset) {
                this._initialized = true;
            }

            return this._initialized ? 0 : 1;
        }

    }

    rendering.setCustomPipeline('Custom', new BuiltinPipelineBuilder());

} // if (rendering)
