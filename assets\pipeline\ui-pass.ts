import {
    assert, gfx, renderer, rendering,
} from 'cc';

import {
    CameraConfigs,
    PipelineConfigs,
    PipelineContext, UIPassConfigs
} from './pipeline-configs';

const { ClearFlagBit, LoadOp, StoreOp } = gfx;

/**
 * 内置UI渲染Pass构建器
 */
export class BuiltinUiPassBuilder implements rendering.PipelinePassBuilder {
    getConfigOrder(): number {
        return 1000;
    }
    getRenderOrder(): number {
        return 1000;
    }
    configCamera(
        camera: Readonly<renderer.scene.Camera>,
        pipelineConfigs: Readonly<PipelineConfigs>,
        cameraConfigs: CameraConfigs & UIPassConfigs): void {
        cameraConfigs.enableUI = true;
        if (cameraConfigs.enableUI) {
            ++cameraConfigs.remainingPasses;
        }
    }
    windowResize(
        ppl: rendering.BasicPipeline,
        pplConfigs: Readonly<PipelineConfigs>,
        cameraConfigs: CameraConfigs & UIPassConfigs,
        window: renderer.RenderWindow): void {
        // UI pass doesn't need additional render targets
    }

    setup(
        ppl: rendering.BasicPipeline,
        pplConfigs: Readonly<PipelineConfigs>,
        cameraConfigs: CameraConfigs & UIPassConfigs,
        camera: renderer.scene.Camera,
        context: PipelineContext,
        prevRenderPass?: rendering.BasicRenderPassBuilder)
        : rendering.BasicRenderPassBuilder | undefined {
        if (!cameraConfigs.enableUI) {
            return prevRenderPass;
        }

        --cameraConfigs.remainingPasses;
        assert(cameraConfigs.remainingPasses >= 0);

        const QueueHint = rendering.QueueHint;
        const SceneFlags = rendering.SceneFlags;

        // UI pass
        const pass = ppl.addRenderPass(cameraConfigs.width, cameraConfigs.height, 'default');
        pass.name = 'UIPass';
        pass.setViewport(camera.viewport);
        pass.addRenderTarget(cameraConfigs.colorName, LoadOp.LOAD, StoreOp.STORE);

        if (camera.clearFlag & ClearFlagBit.DEPTH_STENCIL) {
            pass.addDepthStencil(
                cameraConfigs.depthStencilName,
                LoadOp.CLEAR,
                StoreOp.DISCARD,
                camera.clearDepth,
                camera.clearStencil,
                camera.clearFlag & ClearFlagBit.DEPTH_STENCIL,
            );
        } else {
            pass.addDepthStencil(cameraConfigs.depthStencilName, LoadOp.LOAD, StoreOp.DISCARD);
        }

        const sceneFlags = SceneFlags.UI;
        pass
            .addQueue(QueueHint.BLEND)
            .addScene(camera, sceneFlags);

        return pass;
    }
}
