/**
 * 内置渲染管线 - 重构后的主入口文件
 *
 * 此文件重新导出所有提取的模块以保持向后兼容性
 * 原始的大型 builtin-pipeline.ts 文件已被拆分为多个小文件：
 * - pipeline-configs.ts: 管线配置类和接口
 * - pipeline-utils.ts: 公共工具函数
 * - forward-pass.ts: 前向渲染Pass
 * - bloom-pass.ts: 泛光效果Pass
 * - tone-mapping-pass.ts: 色调映射Pass
 * - fxaa-pass.ts: FXAA抗锯齿Pass
 * - fsr-pass.ts: FSR超分辨率Pass
 * - ui-pass.ts: UI渲染Pass
 * - builtin-pipeline-main.ts: 主要的管线构建器
 */

// 重新导出所有提取的模块以保持向后兼容性
export * from './bloom-pass';
export * from './forward-pass';
export * from './fsr-pass';
export * from './fxaa-pass';
export * from './pipeline-configs';
export * from './pipeline-utils';
export * from './tone-mapping-pass';
export * from './ui-pass';

// 导入主要的管线构建器，这会注册管线到渲染系统
import './builtin-pipeline-main';
