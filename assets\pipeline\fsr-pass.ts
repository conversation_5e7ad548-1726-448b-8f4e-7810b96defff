import {
    assert, gfx,
    renderer, rendering, Vec4
} from 'cc';

import {
    CameraConfigs,
    FSRPassConfigs,
    PipelineConfigs,
    PipelineContext
} from './pipeline-configs';
import { addCopyToScreenPass } from './pipeline-utils';

const { LoadOp, StoreOp } = gfx;

/**
 * 内置FSR超分辨率Pass构建器
 */
export class BuiltinFsrPassBuilder implements rendering.PipelinePassBuilder {
    getConfigOrder(): number {
        return 0;
    }
    getRenderOrder(): number {
        return 500;
    }
    configCamera(
        camera: Readonly<renderer.scene.Camera>,
        pipelineConfigs: Readonly<PipelineConfigs>,
        cameraConfigs: CameraConfigs & FSRPassConfigs): void {
        cameraConfigs.enableFSR
            = cameraConfigs.settings.fsr.enabled
            && !!cameraConfigs.settings.fsr.material;
        if (cameraConfigs.enableFSR) {
            ++cameraConfigs.remainingPasses;
        }
    }
    windowResize(
        ppl: rendering.BasicPipeline,
        pplConfigs: Readonly<PipelineConfigs>,
        cameraConfigs: CameraConfigs & FSRPassConfigs,
        window: renderer.RenderWindow): void {
        if (cameraConfigs.enableFSR) {
            const id = window.renderWindowId;
            ppl.addRenderTarget(`FsrColor${id}`,
                cameraConfigs.radianceFormat, cameraConfigs.width, cameraConfigs.height);
        }
    }

    setup(
        ppl: rendering.BasicPipeline,
        pplConfigs: Readonly<PipelineConfigs>,
        cameraConfigs: CameraConfigs & FSRPassConfigs,
        camera: renderer.scene.Camera,
        context: PipelineContext,
        prevRenderPass?: rendering.BasicRenderPassBuilder)
        : rendering.BasicRenderPassBuilder | undefined {
        if (!cameraConfigs.enableFSR) {
            return prevRenderPass;
        }

        --cameraConfigs.remainingPasses;
        assert(cameraConfigs.remainingPasses >= 0);
        assert(!!cameraConfigs.settings.fsr.material);

        const id = camera.window.renderWindowId;
        const QueueHint = rendering.QueueHint;

        // FSR pass
        const pass = ppl.addRenderPass(cameraConfigs.width, cameraConfigs.height, 'cc-fsr');
        if (cameraConfigs.remainingPasses === 0) {
            pass.addRenderTarget(cameraConfigs.colorName, LoadOp.CLEAR, StoreOp.STORE);
        } else {
            pass.addRenderTarget(`FsrColor${id}`, LoadOp.CLEAR, StoreOp.STORE);
        }
        pass.addTexture(context.colorName, 'outputResultMap');
        
        // Setup FSR parameters
        this._fsrParams.x = cameraConfigs.settings.fsr.sharpness;
        pass.setVec4('g_platform', pplConfigs.platform);
        pass.setVec4('fsrParams', this._fsrParams);
        pass
            .addQueue(QueueHint.OPAQUE)
            .addFullscreenQuad(cameraConfigs.settings.fsr.material, 0);

        if (cameraConfigs.remainingPasses === 0) {
            return pass;
        } else {
            return addCopyToScreenPass(ppl, pplConfigs, cameraConfigs, `FsrColor${id}`);
        }
    }

    // FSR
    private readonly _fsrParams = new Vec4(0, 0, 0, 0);
}
